import type { CollectionConfig } from 'payload'
import { authenticated } from '../access/authenticated'

export const Customers: CollectionConfig = {
  slug: 'customers',
  auth: true,
  access: {
    create: () => true,
    delete: authenticated,
    read: authenticated,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'email', 'phone'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'phone',
      type: 'text',
    },
    {
      name: 'profileImage',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'addresses',
      type: 'array',
      fields: [
        {
          name: 'label',
          type: 'text',
          required: true,
        },
        {
          name: 'street',
          type: 'text',
          required: true,
        },
        {
          name: 'city',
          type: 'text',
          required: true,
        },
        {
          name: 'zipCode',
          type: 'text',
        },
        {
          name: 'isDefault',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'favoriteRestaurants',
      type: 'relationship',
      relationTo: 'restaurants',
      hasMany: true,
    },
    {
      name: 'totalOrders',
      type: 'number',
      defaultValue: 0,
    },
    {
      name: 'totalSpent',
      type: 'number',
      defaultValue: 0,
    },
  ],
}
